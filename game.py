import pygame
from player import Player
from game_map import GameMap
from ui import UI
from monster import Monster, BattleSystem
from shop import Shop, ItemManager
from sound_manager import sound_manager
from constants import *


class Game:
    """游戏主类"""

    def __init__(self, screen):
        self.screen = screen
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()

        # 初始化游戏组件
        self.player = Player(1, 1)  # 玩家起始位置
        self.game_map = GameMap()
        self.ui = UI(screen)
        self.battle_system = BattleSystem()
        self.shop = Shop()
        self.item_manager = ItemManager()

        # 初始化道具
        self.item_manager.init_floor_items()

        # 游戏状态
        self.current_floor = 1
        self.game_state = GAME_STATE_PLAYING

        # 字体
        pygame.font.init()
        self.font = pygame.font.Font(None, 24)

    def handle_event(self, event):
        """处理游戏事件"""
        if self.game_state == GAME_STATE_PLAYING:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    self.move_player(0, -1)
                elif event.key == pygame.K_DOWN:
                    self.move_player(0, 1)
                elif event.key == pygame.K_LEFT:
                    self.move_player(-1, 0)
                elif event.key == pygame.K_RIGHT:
                    self.move_player(1, 0)
                elif event.key == pygame.K_SPACE:
                    self.interact()
                elif event.key == pygame.K_ESCAPE:
                    pygame.quit()
                    exit()
        elif self.game_state == GAME_STATE_BATTLE:
            # 战斗状态的事件处理
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    self.battle_system.player_attack()
                    if not self.battle_system.is_battle_active():
                        self.end_battle()
        elif self.game_state == GAME_STATE_SHOP:
            # 商店状态的事件处理
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    self.shop.move_selection("up")
                elif event.key == pygame.K_DOWN:
                    self.shop.move_selection("down")
                elif event.key == pygame.K_SPACE:
                    self.shop.buy_item(self.player)
                elif event.key == pygame.K_ESCAPE:
                    self.shop.close_shop()
                    self.game_state = GAME_STATE_PLAYING

    def move_player(self, dx, dy):
        """移动玩家"""
        new_x = self.player.x + dx
        new_y = self.player.y + dy

        # 检查边界
        if 0 <= new_x < MAP_WIDTH and 0 <= new_y < MAP_HEIGHT:
            # 检查是否可以移动到目标位置
            if self.game_map.can_move_to(new_x, new_y, self.current_floor):
                self.player.move(dx, dy)
                sound_manager.play_sound("move")

                # 检查是否触发事件
                self.check_tile_event(new_x, new_y)

    def interact(self):
        """交互操作"""
        # 检查当前位置的交互对象
        tile_type = self.game_map.get_tile(
            self.player.x, self.player.y, self.current_floor
        )

        if tile_type == TILE_DOOR_YELLOW and self.player.has_item(ITEM_KEY_YELLOW):
            self.player.use_item(ITEM_KEY_YELLOW)
            self.game_map.set_tile(
                self.player.x, self.player.y, self.current_floor, TILE_EMPTY
            )
            sound_manager.play_sound("door_open")
        elif tile_type == TILE_DOOR_BLUE and self.player.has_item(ITEM_KEY_BLUE):
            self.player.use_item(ITEM_KEY_BLUE)
            self.game_map.set_tile(
                self.player.x, self.player.y, self.current_floor, TILE_EMPTY
            )
            sound_manager.play_sound("door_open")
        elif tile_type == TILE_DOOR_RED and self.player.has_item(ITEM_KEY_RED):
            self.player.use_item(ITEM_KEY_RED)
            self.game_map.set_tile(
                self.player.x, self.player.y, self.current_floor, TILE_EMPTY
            )
            sound_manager.play_sound("door_open")

    def check_tile_event(self, x, y):
        """检查地块事件"""
        tile_type = self.game_map.get_tile(x, y, self.current_floor)

        if tile_type == TILE_MONSTER:
            self.start_battle(x, y)
        elif tile_type == TILE_ITEM:
            self.collect_item(x, y)
        elif tile_type == TILE_STAIRS_UP:
            self.change_floor(1)
        elif tile_type == TILE_STAIRS_DOWN:
            self.change_floor(-1)
        elif tile_type == TILE_SHOP:
            self.open_shop()

    def start_battle(self, x, y):
        """开始战斗"""
        # 创建怪物（根据楼层决定怪物类型）
        monster_type = self.get_monster_type_for_floor(self.current_floor)
        monster = Monster(monster_type, x, y)

        # 开始战斗
        self.battle_system.start_battle(self.player, monster)
        self.game_state = GAME_STATE_BATTLE
        sound_manager.play_sound("attack")

    def get_monster_type_for_floor(self, floor):
        """根据楼层获取怪物类型"""
        if floor <= 5:
            return MONSTER_SLIME
        elif floor <= 10:
            return MONSTER_SKELETON
        elif floor <= 15:
            return MONSTER_ORC
        else:
            return MONSTER_DRAGON

    def end_battle(self):
        """结束战斗"""
        battle = self.battle_system.get_current_battle()
        if battle and battle["result"] == "victory":
            # 移除怪物
            self.game_map.set_tile(
                self.player.x, self.player.y, self.current_floor, TILE_EMPTY
            )
            sound_manager.play_sound("victory")
        elif battle and battle["result"] == "defeat":
            # 游戏结束
            self.game_state = GAME_STATE_GAME_OVER
            sound_manager.play_sound("defeat")
            return

        self.game_state = GAME_STATE_PLAYING
        self.battle_system.current_battle = None

    def collect_item(self, x, y):
        """收集道具"""
        # 从道具管理器获取道具
        item = self.item_manager.remove_floor_item(self.current_floor, x, y)
        if item:
            self.player.add_item(item.type)
            sound_manager.play_sound("item_pickup")

        # 移除地图上的道具标记
        self.game_map.set_tile(x, y, self.current_floor, TILE_EMPTY)

    def open_shop(self):
        """打开商店"""
        self.shop.open_shop()
        self.game_state = GAME_STATE_SHOP
        sound_manager.play_sound("shop")

    def change_floor(self, direction):
        """切换楼层"""
        new_floor = self.current_floor + direction
        if 1 <= new_floor <= MAX_FLOORS:
            self.current_floor = new_floor
            # 设置玩家在新楼层的位置
            if direction > 0:  # 上楼
                self.player.x, self.player.y = self.game_map.get_stairs_down_pos(
                    new_floor
                )
            else:  # 下楼
                self.player.x, self.player.y = self.game_map.get_stairs_up_pos(
                    new_floor
                )

    def update(self):
        """更新游戏状态"""
        self.player.update()

    def render(self):
        """渲染游戏"""
        self.screen.fill(BLACK)

        # 渲染地图
        self.game_map.render(self.screen, self.current_floor)

        # 渲染道具
        map_pixel_width = MAP_WIDTH * TILE_SIZE
        map_pixel_height = MAP_HEIGHT * TILE_SIZE
        offset_x = (self.screen_width - map_pixel_width) // 2
        offset_y = (self.screen_height - map_pixel_height) // 2
        self.item_manager.render_floor_items(
            self.screen, self.current_floor, offset_x, offset_y
        )

        # 渲染玩家
        self.player.render(self.screen)

        # 渲染UI
        self.ui.render(self.player, self.current_floor)

        # 根据游戏状态渲染不同界面
        if self.game_state == GAME_STATE_BATTLE:
            self.render_battle()
        elif self.game_state == GAME_STATE_SHOP:
            self.shop.render(self.screen)
        elif self.game_state == GAME_STATE_GAME_OVER:
            self.render_game_over()

        pygame.display.flip()

    def render_battle(self):
        """渲染战斗界面"""
        battle = self.battle_system.get_current_battle()
        if not battle:
            return

        # 使用UI类的战斗界面渲染
        self.ui.render_battle_ui(battle["player"], battle["monster"])

        # 显示战斗日志
        battle_log = self.battle_system.get_battle_log()
        y_offset = 50
        for log_entry in battle_log[-5:]:  # 显示最近5条日志
            log_text = self.font.render(log_entry, True, WHITE)
            self.screen.blit(log_text, (50, y_offset))
            y_offset += 25

    def render_game_over(self):
        """渲染游戏结束界面"""
        # 创建半透明覆盖层
        overlay = pygame.Surface((self.screen_width, self.screen_height))
        overlay.set_alpha(200)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))

        # 游戏结束文本
        game_over_text = pygame.font.SysFont("segoeuisymbol", 64).render(
            "游戏结束", True, RED
        )
        text_rect = game_over_text.get_rect(
            center=(self.screen_width // 2, self.screen_height // 2 - 50)
        )
        self.screen.blit(game_over_text, text_rect)

        # 统计信息
        stats_text = [
            f"到达楼层: {self.current_floor}",
            f"最终等级: {self.player.level}",
            f"获得金币: {self.player.gold}",
        ]

        y_offset = self.screen_height // 2 + 20
        for stat in stats_text:
            stat_surface = self.font.render(stat, True, WHITE)
            stat_rect = stat_surface.get_rect(center=(self.screen_width // 2, y_offset))
            self.screen.blit(stat_surface, stat_rect)
            y_offset += 30

        # 重新开始提示
        restart_text = self.font.render("按ESC键退出", True, YELLOW)
        restart_rect = restart_text.get_rect(
            center=(self.screen_width // 2, self.screen_height // 2 + 150)
        )
        self.screen.blit(restart_text, restart_rect)
