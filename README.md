# 魔塔小游戏

一个使用Python和Pygame制作的经典魔塔游戏。

## 游戏特色

- 🏰 多层塔楼探索
- ⚔️ 回合制战斗系统
- 🎒 道具收集和管理
- 🏪 商店购买系统
- 🔊 音效和背景音乐
- 📊 角色属性和等级系统

## 安装要求

- Python 3.7+
- Pygame 2.0+

安装Pygame：
```bash
pip install pygame
```

## 运行游戏

```bash
python main.py
```

## 游戏操作

### 基本控制
- **方向键**: 移动角色
- **空格键**: 交互/确认/攻击
- **ESC键**: 退出游戏

### 游戏元素

#### 地图符号
- **P**: 玩家角色
- **M**: 怪物
- **I**: 道具
- **Y/B/R**: 黄/蓝/红门
- **K**: 钥匙
- **S**: 商店
- **↑/↓**: 上楼梯/下楼梯

#### 道具类型
- **黄钥匙**: 打开黄色门
- **蓝钥匙**: 打开蓝色门  
- **红钥匙**: 打开红色门
- **红药水**: 恢复50点生命值
- **蓝药水**: 恢复30点魔法值
- **剑**: 增加10点攻击力
- **盾**: 增加5点防御力

#### 怪物类型
- **史莱姆** (1-5层): 基础怪物
- **骷髅** (6-10层): 中等强度
- **兽人** (11-15层): 高等强度
- **龙** (16-20层): 最强怪物

## 游戏系统

### 战斗系统
- 玩家先手攻击
- 伤害计算: 攻击力 - 防御力 (最少1点伤害)
- 击败怪物获得金币和经验值

### 等级系统
- 获得经验值自动升级
- 升级提升生命值、攻击力和防御力
- 升级时完全恢复生命值

### 商店系统
- 在商店地块按空格键进入
- 上下键选择物品
- 空格键购买
- ESC键退出商店

## 文件结构

```
tots/
├── main.py              # 游戏入口
├── game.py              # 游戏主逻辑
├── player.py            # 玩家角色类
├── monster.py           # 怪物和战斗系统
├── game_map.py          # 地图系统
├── shop.py              # 商店和道具系统
├── ui.py                # 用户界面
├── sound_manager.py     # 音效管理
├── constants.py         # 游戏常量
└── README.md            # 说明文档
```

## 游戏目标

- 探索塔楼的每一层
- 收集钥匙打开门
- 击败怪物获得经验和金币
- 在商店购买装备提升实力
- 挑战更高层的强大怪物

## 开发说明

这是一个教学项目，展示了如何使用Python和Pygame开发一个完整的游戏。代码结构清晰，易于理解和扩展。

### 可扩展功能
- 添加更多怪物类型
- 增加魔法系统
- 添加更多道具和装备
- 实现存档功能
- 添加更多楼层和关卡

## 许可证

MIT License

## 贡献

欢迎提交问题和改进建议！
