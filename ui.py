import pygame
from constants import *

class UI:
    """游戏UI类"""
    
    def __init__(self, screen):
        self.screen = screen
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()
        
        # 字体
        pygame.font.init()
        self.font_large = pygame.font.Font(None, 32)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
        # UI面板位置
        self.info_panel_width = 200
        self.info_panel_height = self.screen_height
        self.info_panel_x = self.screen_width - self.info_panel_width
        self.info_panel_y = 0
    
    def render(self, player, current_floor):
        """渲染UI"""
        self.render_info_panel(player, current_floor)
        self.render_controls()
    
    def render_info_panel(self, player, current_floor):
        """渲染信息面板"""
        # 绘制面板背景
        panel_rect = pygame.Rect(
            self.info_panel_x, self.info_panel_y,
            self.info_panel_width, self.info_panel_height
        )
        pygame.draw.rect(self.screen, (40, 40, 40), panel_rect)
        pygame.draw.rect(self.screen, WHITE, panel_rect, 2)
        
        # 获取玩家状态
        status = player.get_status_info()
        
        y_offset = 20
        line_height = 25
        
        # 标题
        title = self.font_large.render("状态", True, WHITE)
        self.screen.blit(title, (self.info_panel_x + 10, y_offset))
        y_offset += 40
        
        # 楼层信息
        floor_text = self.font_medium.render(f"楼层: {current_floor}", True, WHITE)
        self.screen.blit(floor_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 等级
        level_text = self.font_medium.render(f"等级: {status['level']}", True, WHITE)
        self.screen.blit(level_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 生命值
        hp_text = self.font_medium.render(f"生命: {status['hp']}/{status['max_hp']}", True, RED)
        self.screen.blit(hp_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 生命值条
        hp_bar_width = self.info_panel_width - 20
        hp_bar_height = 10
        hp_percentage = status['hp'] / status['max_hp'] if status['max_hp'] > 0 else 0
        
        # 背景条
        hp_bg_rect = pygame.Rect(self.info_panel_x + 10, y_offset, hp_bar_width, hp_bar_height)
        pygame.draw.rect(self.screen, GRAY, hp_bg_rect)
        
        # 生命值条
        hp_fill_width = int(hp_bar_width * hp_percentage)
        if hp_fill_width > 0:
            hp_fill_rect = pygame.Rect(self.info_panel_x + 10, y_offset, hp_fill_width, hp_bar_height)
            pygame.draw.rect(self.screen, RED, hp_fill_rect)
        
        pygame.draw.rect(self.screen, WHITE, hp_bg_rect, 1)
        y_offset += 30
        
        # 攻击力
        attack_text = self.font_medium.render(f"攻击: {status['attack']}", True, WHITE)
        self.screen.blit(attack_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 防御力
        defense_text = self.font_medium.render(f"防御: {status['defense']}", True, WHITE)
        self.screen.blit(defense_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 金币
        gold_text = self.font_medium.render(f"金币: {status['gold']}", True, YELLOW)
        self.screen.blit(gold_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 经验值
        exp_text = self.font_medium.render(f"经验: {status['exp']}/{status['exp_to_next']}", True, GREEN)
        self.screen.blit(exp_text, (self.info_panel_x + 10, y_offset))
        y_offset += line_height
        
        # 经验值条
        exp_percentage = status['exp'] / status['exp_to_next'] if status['exp_to_next'] > 0 else 0
        
        # 背景条
        exp_bg_rect = pygame.Rect(self.info_panel_x + 10, y_offset, hp_bar_width, hp_bar_height)
        pygame.draw.rect(self.screen, GRAY, exp_bg_rect)
        
        # 经验值条
        exp_fill_width = int(hp_bar_width * exp_percentage)
        if exp_fill_width > 0:
            exp_fill_rect = pygame.Rect(self.info_panel_x + 10, y_offset, exp_fill_width, hp_bar_height)
            pygame.draw.rect(self.screen, GREEN, exp_fill_rect)
        
        pygame.draw.rect(self.screen, WHITE, exp_bg_rect, 1)
        y_offset += 40
        
        # 背包标题
        inventory_title = self.font_medium.render("背包:", True, WHITE)
        self.screen.blit(inventory_title, (self.info_panel_x + 10, y_offset))
        y_offset += 30
        
        # 背包物品
        item_names = {
            ITEM_KEY_YELLOW: "黄钥匙",
            ITEM_KEY_BLUE: "蓝钥匙",
            ITEM_KEY_RED: "红钥匙",
            ITEM_POTION_RED: "红药水",
            ITEM_POTION_BLUE: "蓝药水",
            ITEM_SWORD: "剑",
            ITEM_SHIELD: "盾"
        }
        
        for item_type, count in status['inventory'].items():
            if count > 0:
                item_name = item_names.get(item_type, f"物品{item_type}")
                item_text = self.font_small.render(f"{item_name}: {count}", True, WHITE)
                self.screen.blit(item_text, (self.info_panel_x + 15, y_offset))
                y_offset += 20
    
    def render_controls(self):
        """渲染控制说明"""
        controls = [
            "控制说明:",
            "方向键: 移动",
            "空格键: 交互/确认",
            "ESC: 退出"
        ]
        
        y_start = self.screen_height - len(controls) * 20 - 20
        
        for i, control in enumerate(controls):
            if i == 0:
                text = self.font_medium.render(control, True, YELLOW)
            else:
                text = self.font_small.render(control, True, WHITE)
            
            self.screen.blit(text, (10, y_start + i * 20))
    
    def render_message(self, message, duration=2000):
        """渲染消息提示"""
        # 这里可以实现消息显示系统
        pass
    
    def render_battle_ui(self, player, monster):
        """渲染战斗界面"""
        # 创建战斗面板
        panel_width = 400
        panel_height = 200
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = (self.screen_height - panel_height) // 2
        
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(self.screen, (60, 60, 60), panel_rect)
        pygame.draw.rect(self.screen, WHITE, panel_rect, 3)
        
        # 战斗标题
        title = self.font_large.render("战斗!", True, RED)
        title_rect = title.get_rect(center=(panel_x + panel_width//2, panel_y + 30))
        self.screen.blit(title, title_rect)
        
        # 玩家信息
        player_info = f"玩家 HP: {player.hp}/{player.max_hp}"
        player_text = self.font_medium.render(player_info, True, WHITE)
        self.screen.blit(player_text, (panel_x + 20, panel_y + 70))
        
        # 怪物信息（如果有怪物类的话）
        monster_info = "怪物 HP: ???"
        monster_text = self.font_medium.render(monster_info, True, WHITE)
        self.screen.blit(monster_text, (panel_x + 20, panel_y + 100))
        
        # 操作提示
        hint = "按空格键攻击"
        hint_text = self.font_small.render(hint, True, YELLOW)
        hint_rect = hint_text.get_rect(center=(panel_x + panel_width//2, panel_y + 150))
        self.screen.blit(hint_text, hint_rect)
