# 游戏常量定义

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
BROWN = (139, 69, 19)
PURPLE = (128, 0, 128)

# 地图设置
MAP_WIDTH = 11
MAP_HEIGHT = 11
TILE_SIZE = 32
MAX_FLOORS = 20

# 地块类型
TILE_EMPTY = 0
TILE_WALL = 1
TILE_PLAYER = 2
TILE_MONSTER = 3
TILE_ITEM = 4
TILE_DOOR_YELLOW = 5
TILE_DOOR_BLUE = 6
TILE_DOOR_RED = 7
TILE_STAIRS_UP = 8
TILE_STAIRS_DOWN = 9
TILE_SHOP = 10
TILE_NPC = 11

# 道具类型
ITEM_KEY_YELLOW = 1
ITEM_KEY_BLUE = 2
ITEM_KEY_RED = 3
ITEM_POTION_RED = 4
ITEM_POTION_BLUE = 5
ITEM_SWORD = 6
ITEM_SHIELD = 7

# 怪物类型
MONSTER_SLIME = 1
MONSTER_SKELETON = 2
MONSTER_ORC = 3
MONSTER_DRAGON = 4

# 游戏状态
GAME_STATE_MENU = 0
GAME_STATE_PLAYING = 1
GAME_STATE_BATTLE = 2
GAME_STATE_SHOP = 3
GAME_STATE_GAME_OVER = 4

# 玩家初始属性
PLAYER_INITIAL_HP = 100
PLAYER_INITIAL_ATTACK = 10
PLAYER_INITIAL_DEFENSE = 5
PLAYER_INITIAL_GOLD = 0

# 地图颜色映射
TILE_COLORS = {
    TILE_EMPTY: BLACK,
    TILE_WALL: GRAY,
    TILE_PLAYER: BLUE,
    TILE_MONSTER: RED,
    TILE_ITEM: YELLOW,
    TILE_DOOR_YELLOW: YELLOW,
    TILE_DOOR_BLUE: BLUE,
    TILE_DOOR_RED: RED,
    TILE_STAIRS_UP: GREEN,
    TILE_STAIRS_DOWN: PURPLE,
    TILE_SHOP: BROWN,
    TILE_NPC: WHITE
}
