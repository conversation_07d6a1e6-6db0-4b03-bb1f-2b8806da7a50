import pygame
import os

class SoundManager:
    """音效管理器"""
    
    def __init__(self):
        # 初始化音频系统
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        # 音效字典
        self.sounds = {}
        self.music_volume = 0.5
        self.sound_volume = 0.7
        
        # 尝试加载音效文件
        self.load_sounds()
        
        # 音效开关
        self.sound_enabled = True
        self.music_enabled = True
    
    def load_sounds(self):
        """加载音效文件"""
        # 定义音效文件路径（如果存在的话）
        sound_files = {
            'move': 'sounds/move.wav',
            'attack': 'sounds/attack.wav',
            'item_pickup': 'sounds/pickup.wav',
            'door_open': 'sounds/door.wav',
            'level_up': 'sounds/levelup.wav',
            'victory': 'sounds/victory.wav',
            'defeat': 'sounds/defeat.wav',
            'shop': 'sounds/shop.wav'
        }
        
        for sound_name, file_path in sound_files.items():
            try:
                if os.path.exists(file_path):
                    sound = pygame.mixer.Sound(file_path)
                    sound.set_volume(self.sound_volume)
                    self.sounds[sound_name] = sound
                else:
                    # 如果文件不存在，创建一个空的音效对象
                    self.sounds[sound_name] = None
            except pygame.error:
                # 如果加载失败，设置为None
                self.sounds[sound_name] = None
    
    def play_sound(self, sound_name):
        """播放音效"""
        if not self.sound_enabled:
            return
        
        if sound_name in self.sounds and self.sounds[sound_name] is not None:
            try:
                self.sounds[sound_name].play()
            except pygame.error:
                pass  # 忽略播放错误
    
    def play_music(self, music_file, loop=-1):
        """播放背景音乐"""
        if not self.music_enabled:
            return
        
        try:
            if os.path.exists(music_file):
                pygame.mixer.music.load(music_file)
                pygame.mixer.music.set_volume(self.music_volume)
                pygame.mixer.music.play(loop)
        except pygame.error:
            pass  # 忽略播放错误
    
    def stop_music(self):
        """停止背景音乐"""
        try:
            pygame.mixer.music.stop()
        except pygame.error:
            pass
    
    def set_sound_volume(self, volume):
        """设置音效音量"""
        self.sound_volume = max(0.0, min(1.0, volume))
        for sound in self.sounds.values():
            if sound is not None:
                sound.set_volume(self.sound_volume)
    
    def set_music_volume(self, volume):
        """设置音乐音量"""
        self.music_volume = max(0.0, min(1.0, volume))
        try:
            pygame.mixer.music.set_volume(self.music_volume)
        except pygame.error:
            pass
    
    def toggle_sound(self):
        """切换音效开关"""
        self.sound_enabled = not self.sound_enabled
        return self.sound_enabled
    
    def toggle_music(self):
        """切换音乐开关"""
        self.music_enabled = not self.music_enabled
        if not self.music_enabled:
            self.stop_music()
        return self.music_enabled
    
    def create_simple_sound(self, frequency, duration):
        """创建简单的音效（如果没有音效文件）"""
        try:
            sample_rate = 22050
            frames = int(duration * sample_rate)
            arr = []
            
            for i in range(frames):
                time = float(i) / sample_rate
                wave = 4096 * (0.5 * (1 + (time * frequency) % 1))
                arr.append([int(wave), int(wave)])
            
            sound = pygame.sndarray.make_sound(arr)
            return sound
        except:
            return None
    
    def init_default_sounds(self):
        """初始化默认音效（程序生成）"""
        # 如果没有音效文件，创建简单的音效
        if self.sounds['move'] is None:
            self.sounds['move'] = self.create_simple_sound(200, 0.1)
        
        if self.sounds['attack'] is None:
            self.sounds['attack'] = self.create_simple_sound(150, 0.2)
        
        if self.sounds['item_pickup'] is None:
            self.sounds['item_pickup'] = self.create_simple_sound(400, 0.15)
        
        if self.sounds['door_open'] is None:
            self.sounds['door_open'] = self.create_simple_sound(300, 0.3)
        
        if self.sounds['level_up'] is None:
            self.sounds['level_up'] = self.create_simple_sound(500, 0.5)
        
        if self.sounds['victory'] is None:
            self.sounds['victory'] = self.create_simple_sound(600, 0.8)
        
        if self.sounds['defeat'] is None:
            self.sounds['defeat'] = self.create_simple_sound(100, 1.0)
        
        if self.sounds['shop'] is None:
            self.sounds['shop'] = self.create_simple_sound(350, 0.2)
        
        # 设置音量
        for sound in self.sounds.values():
            if sound is not None:
                sound.set_volume(self.sound_volume)

# 全局音效管理器实例
sound_manager = SoundManager()
sound_manager.init_default_sounds()
