import pygame
from constants import *

class Monster:
    """怪物基类"""
    
    def __init__(self, monster_type, x, y):
        self.type = monster_type
        self.x = x
        self.y = y
        
        # 根据怪物类型设置属性
        self.init_stats()
        
        # 状态
        self.is_alive = True
        
    def init_stats(self):
        """根据怪物类型初始化属性"""
        monster_data = {
            MONSTER_SLIME: {
                'name': '史莱姆',
                'hp': 30,
                'attack': 8,
                'defense': 2,
                'gold': 5,
                'exp': 10,
                'color': GREEN
            },
            MONSTER_SKELETON: {
                'name': '骷髅',
                'hp': 50,
                'attack': 15,
                'defense': 5,
                'gold': 10,
                'exp': 20,
                'color': WHITE
            },
            MONSTER_ORC: {
                'name': '兽人',
                'hp': 80,
                'attack': 25,
                'defense': 8,
                'gold': 20,
                'exp': 35,
                'color': (139, 69, 19)  # 棕色
            },
            MONSTER_DRAGON: {
                'name': '龙',
                'hp': 200,
                'attack': 50,
                'defense': 15,
                'gold': 100,
                'exp': 100,
                'color': RED
            }
        }
        
        data = monster_data.get(self.type, monster_data[MONSTER_SLIME])
        
        self.name = data['name']
        self.max_hp = data['hp']
        self.hp = self.max_hp
        self.attack = data['attack']
        self.defense = data['defense']
        self.gold_reward = data['gold']
        self.exp_reward = data['exp']
        self.color = data['color']
    
    def take_damage(self, damage):
        """受到伤害"""
        actual_damage = max(1, damage - self.defense)
        self.hp = max(0, self.hp - actual_damage)
        
        if self.hp <= 0:
            self.is_alive = False
        
        return actual_damage
    
    def get_attack_damage(self):
        """获取攻击伤害"""
        return self.attack
    
    def get_rewards(self):
        """获取击败怪物的奖励"""
        return {
            'gold': self.gold_reward,
            'exp': self.exp_reward
        }
    
    def render(self, screen, offset_x, offset_y):
        """渲染怪物"""
        screen_x = offset_x + self.x * TILE_SIZE
        screen_y = offset_y + self.y * TILE_SIZE
        
        monster_rect = pygame.Rect(screen_x, screen_y, TILE_SIZE, TILE_SIZE)
        pygame.draw.rect(screen, self.color, monster_rect)
        pygame.draw.rect(screen, BLACK, monster_rect, 2)
        
        # 绘制怪物符号
        font = pygame.font.Font(None, 20)
        text = font.render("M", True, BLACK)
        text_rect = text.get_rect(center=monster_rect.center)
        screen.blit(text, text_rect)

class BattleSystem:
    """战斗系统"""
    
    def __init__(self):
        self.current_battle = None
        self.battle_log = []
        
    def start_battle(self, player, monster):
        """开始战斗"""
        self.current_battle = {
            'player': player,
            'monster': monster,
            'turn': 'player',  # 玩家先手
            'result': None
        }
        self.battle_log = []
        self.add_log(f"遭遇了 {monster.name}!")
        
        return self.current_battle
    
    def add_log(self, message):
        """添加战斗日志"""
        self.battle_log.append(message)
        if len(self.battle_log) > 10:  # 保持最多10条记录
            self.battle_log.pop(0)
    
    def player_attack(self):
        """玩家攻击"""
        if not self.current_battle or self.current_battle['turn'] != 'player':
            return False
        
        player = self.current_battle['player']
        monster = self.current_battle['monster']
        
        # 计算伤害
        damage = player.get_total_attack()
        actual_damage = monster.take_damage(damage)
        
        self.add_log(f"你对 {monster.name} 造成了 {actual_damage} 点伤害")
        
        # 检查怪物是否死亡
        if not monster.is_alive:
            self.end_battle('victory')
            return True
        
        # 切换到怪物回合
        self.current_battle['turn'] = 'monster'
        self.monster_attack()
        
        return True
    
    def monster_attack(self):
        """怪物攻击"""
        if not self.current_battle:
            return
        
        player = self.current_battle['player']
        monster = self.current_battle['monster']
        
        # 计算伤害
        damage = monster.get_attack_damage()
        actual_damage = player.take_damage(damage)
        
        self.add_log(f"{monster.name} 对你造成了 {actual_damage} 点伤害")
        
        # 检查玩家是否死亡
        if not player.is_alive():
            self.end_battle('defeat')
            return
        
        # 切换到玩家回合
        self.current_battle['turn'] = 'player'
    
    def end_battle(self, result):
        """结束战斗"""
        if not self.current_battle:
            return
        
        self.current_battle['result'] = result
        
        if result == 'victory':
            player = self.current_battle['player']
            monster = self.current_battle['monster']
            rewards = monster.get_rewards()
            
            # 给予奖励
            player.add_gold(rewards['gold'])
            player.add_exp(rewards['exp'])
            
            self.add_log(f"胜利! 获得 {rewards['gold']} 金币和 {rewards['exp']} 经验")
        elif result == 'defeat':
            self.add_log("你被击败了...")
    
    def can_win_battle(self, player, monster):
        """预测战斗结果 - 检查玩家是否能够击败怪物"""
        if player.get_total_attack() <= monster.defense:
            return False  # 无法造成伤害
        
        # 计算需要多少回合击败怪物
        player_damage_per_turn = max(1, player.get_total_attack() - monster.defense)
        turns_to_kill_monster = (monster.hp + player_damage_per_turn - 1) // player_damage_per_turn
        
        # 计算怪物需要多少回合击败玩家
        monster_damage_per_turn = max(1, monster.attack - player.get_total_defense())
        turns_to_kill_player = (player.hp + monster_damage_per_turn - 1) // monster_damage_per_turn
        
        # 玩家先手，所以如果回合数相等，玩家获胜
        return turns_to_kill_monster <= turns_to_kill_player
    
    def get_battle_prediction(self, player, monster):
        """获取战斗预测信息"""
        if player.get_total_attack() <= monster.defense:
            return {
                'can_win': False,
                'player_damage': 0,
                'monster_damage': max(1, monster.attack - player.get_total_defense()),
                'message': "无法对怪物造成伤害!"
            }
        
        player_damage = max(1, player.get_total_attack() - monster.defense)
        monster_damage = max(1, monster.attack - player.get_total_defense())
        
        turns_to_kill_monster = (monster.hp + player_damage - 1) // player_damage
        turns_to_kill_player = (player.hp + monster_damage - 1) // monster_damage
        
        can_win = turns_to_kill_monster <= turns_to_kill_player
        
        if can_win:
            hp_lost = (turns_to_kill_monster - 1) * monster_damage
            message = f"可以获胜，预计损失 {hp_lost} 生命值"
        else:
            message = "无法获胜，建议提升装备后再来"
        
        return {
            'can_win': can_win,
            'player_damage': player_damage,
            'monster_damage': monster_damage,
            'message': message
        }
    
    def is_battle_active(self):
        """检查是否正在战斗"""
        return self.current_battle is not None and self.current_battle['result'] is None
    
    def get_current_battle(self):
        """获取当前战斗信息"""
        return self.current_battle
    
    def get_battle_log(self):
        """获取战斗日志"""
        return self.battle_log.copy()
