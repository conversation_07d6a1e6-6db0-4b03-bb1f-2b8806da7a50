import pygame
import sys
from game import Game

def main():
    """主函数"""
    pygame.init()
    
    # 设置游戏窗口
    SCREEN_WIDTH = 800
    SCREEN_HEIGHT = 600
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("魔塔小游戏")
    
    # 创建游戏实例
    game = Game(screen)
    
    # 游戏主循环
    clock = pygame.time.Clock()
    running = True
    
    while running:
        # 处理事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            else:
                game.handle_event(event)
        
        # 更新游戏状态
        game.update()
        
        # 渲染游戏
        game.render()
        
        # 控制帧率
        clock.tick(60)
    
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
