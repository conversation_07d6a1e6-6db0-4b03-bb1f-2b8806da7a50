import pygame
from constants import *

class Item:
    """道具类"""
    
    def __init__(self, item_type):
        self.type = item_type
        self.init_properties()
    
    def init_properties(self):
        """初始化道具属性"""
        item_data = {
            ITEM_KEY_YELLOW: {
                'name': '黄钥匙',
                'description': '可以打开黄色门',
                'price': 10,
                'usable': True,
                'stackable': True
            },
            ITEM_KEY_BLUE: {
                'name': '蓝钥匙',
                'description': '可以打开蓝色门',
                'price': 50,
                'usable': True,
                'stackable': True
            },
            ITEM_KEY_RED: {
                'name': '红钥匙',
                'description': '可以打开红色门',
                'price': 100,
                'usable': True,
                'stackable': True
            },
            ITEM_POTION_RED: {
                'name': '红药水',
                'description': '恢复50点生命值',
                'price': 25,
                'usable': True,
                'stackable': True
            },
            ITEM_POTION_BLUE: {
                'name': '蓝药水',
                'description': '恢复30点魔法值',
                'price': 20,
                'usable': True,
                'stackable': True
            },
            ITEM_SWORD: {
                'name': '剑',
                'description': '增加10点攻击力',
                'price': 100,
                'usable': True,
                'stackable': True
            },
            ITEM_SHIELD: {
                'name': '盾',
                'description': '增加5点防御力',
                'price': 80,
                'usable': True,
                'stackable': True
            }
        }
        
        data = item_data.get(self.type, {})
        self.name = data.get('name', '未知道具')
        self.description = data.get('description', '无描述')
        self.price = data.get('price', 0)
        self.usable = data.get('usable', False)
        self.stackable = data.get('stackable', True)

class Shop:
    """商店类"""
    
    def __init__(self):
        self.items = self.init_shop_items()
        self.is_open = False
        self.selected_item = 0
        
    def init_shop_items(self):
        """初始化商店物品"""
        return [
            {'item': Item(ITEM_KEY_YELLOW), 'stock': 10},
            {'item': Item(ITEM_KEY_BLUE), 'stock': 5},
            {'item': Item(ITEM_KEY_RED), 'stock': 3},
            {'item': Item(ITEM_POTION_RED), 'stock': 20},
            {'item': Item(ITEM_POTION_BLUE), 'stock': 15},
            {'item': Item(ITEM_SWORD), 'stock': 5},
            {'item': Item(ITEM_SHIELD), 'stock': 5}
        ]
    
    def open_shop(self):
        """打开商店"""
        self.is_open = True
        self.selected_item = 0
    
    def close_shop(self):
        """关闭商店"""
        self.is_open = False
    
    def move_selection(self, direction):
        """移动选择"""
        if direction == 'up':
            self.selected_item = (self.selected_item - 1) % len(self.items)
        elif direction == 'down':
            self.selected_item = (self.selected_item + 1) % len(self.items)
    
    def buy_item(self, player):
        """购买物品"""
        if not self.is_open or self.selected_item >= len(self.items):
            return False
        
        shop_item = self.items[self.selected_item]
        item = shop_item['item']
        
        # 检查库存
        if shop_item['stock'] <= 0:
            return False
        
        # 检查玩家金币
        if player.gold < item.price:
            return False
        
        # 购买成功
        player.spend_gold(item.price)
        player.add_item(item.type, 1)
        shop_item['stock'] -= 1
        
        return True
    
    def get_selected_item(self):
        """获取当前选中的物品"""
        if 0 <= self.selected_item < len(self.items):
            return self.items[self.selected_item]
        return None
    
    def render(self, screen):
        """渲染商店界面"""
        if not self.is_open:
            return
        
        # 创建商店面板
        panel_width = 500
        panel_height = 400
        panel_x = (screen.get_width() - panel_width) // 2
        panel_y = (screen.get_height() - panel_height) // 2
        
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(screen, (40, 40, 40), panel_rect)
        pygame.draw.rect(screen, WHITE, panel_rect, 3)
        
        # 字体
        font_large = pygame.font.Font(None, 32)
        font_medium = pygame.font.Font(None, 24)
        font_small = pygame.font.Font(None, 18)
        
        # 标题
        title = font_large.render("商店", True, YELLOW)
        title_rect = title.get_rect(center=(panel_x + panel_width//2, panel_y + 30))
        screen.blit(title, title_rect)
        
        # 物品列表
        y_offset = panel_y + 70
        line_height = 35
        
        for i, shop_item in enumerate(self.items):
            item = shop_item['item']
            stock = shop_item['stock']
            
            # 选中高亮
            if i == self.selected_item:
                highlight_rect = pygame.Rect(panel_x + 10, y_offset - 5, panel_width - 20, line_height)
                pygame.draw.rect(screen, (80, 80, 80), highlight_rect)
            
            # 物品名称和价格
            item_text = f"{item.name} - {item.price}金币"
            if stock <= 0:
                item_text += " (售罄)"
                color = GRAY
            else:
                item_text += f" (库存: {stock})"
                color = WHITE
            
            text = font_medium.render(item_text, True, color)
            screen.blit(text, (panel_x + 20, y_offset))
            
            # 物品描述
            desc_text = font_small.render(item.description, True, GRAY)
            screen.blit(desc_text, (panel_x + 20, y_offset + 20))
            
            y_offset += line_height
        
        # 操作提示
        hints = [
            "上下键: 选择物品",
            "空格键: 购买",
            "ESC键: 关闭商店"
        ]
        
        hint_y = panel_y + panel_height - 80
        for hint in hints:
            hint_text = font_small.render(hint, True, YELLOW)
            screen.blit(hint_text, (panel_x + 20, hint_y))
            hint_y += 20

class ItemManager:
    """道具管理器"""
    
    def __init__(self):
        self.floor_items = {}  # 每层楼的道具
        
    def add_floor_item(self, floor, x, y, item_type):
        """在指定楼层位置添加道具"""
        if floor not in self.floor_items:
            self.floor_items[floor] = {}
        
        key = (x, y)
        self.floor_items[floor][key] = Item(item_type)
    
    def get_floor_item(self, floor, x, y):
        """获取指定位置的道具"""
        if floor in self.floor_items:
            key = (x, y)
            return self.floor_items[floor].get(key)
        return None
    
    def remove_floor_item(self, floor, x, y):
        """移除指定位置的道具"""
        if floor in self.floor_items:
            key = (x, y)
            if key in self.floor_items[floor]:
                item = self.floor_items[floor][key]
                del self.floor_items[floor][key]
                return item
        return None
    
    def init_floor_items(self):
        """初始化各楼层的道具"""
        # 第1层
        self.add_floor_item(1, 3, 3, ITEM_KEY_YELLOW)
        self.add_floor_item(1, 9, 7, ITEM_POTION_RED)
        
        # 第2层
        self.add_floor_item(2, 9, 3, ITEM_KEY_BLUE)
        self.add_floor_item(2, 9, 7, ITEM_SWORD)
        
        # 第3层
        self.add_floor_item(3, 3, 3, ITEM_POTION_RED)
        self.add_floor_item(3, 9, 7, ITEM_SHIELD)
    
    def render_floor_items(self, screen, floor, offset_x, offset_y):
        """渲染指定楼层的道具"""
        if floor not in self.floor_items:
            return
        
        font = pygame.font.Font(None, 16)
        
        for (x, y), item in self.floor_items[floor].items():
            screen_x = offset_x + x * TILE_SIZE
            screen_y = offset_y + y * TILE_SIZE
            
            # 绘制道具背景
            item_rect = pygame.Rect(screen_x, screen_y, TILE_SIZE, TILE_SIZE)
            pygame.draw.rect(screen, YELLOW, item_rect)
            pygame.draw.rect(screen, BLACK, item_rect, 1)
            
            # 绘制道具符号
            symbol = "I"
            if item.type in [ITEM_KEY_YELLOW, ITEM_KEY_BLUE, ITEM_KEY_RED]:
                symbol = "K"
            elif item.type in [ITEM_POTION_RED, ITEM_POTION_BLUE]:
                symbol = "P"
            elif item.type == ITEM_SWORD:
                symbol = "S"
            elif item.type == ITEM_SHIELD:
                symbol = "D"
            
            text = font.render(symbol, True, BLACK)
            text_rect = text.get_rect(center=item_rect.center)
            screen.blit(text, text_rect)
