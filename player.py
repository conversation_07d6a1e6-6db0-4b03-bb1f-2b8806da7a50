import pygame
from constants import *
from sound_manager import sound_manager


class Player:
    """玩家角色类"""

    def __init__(self, x, y):
        self.x = x
        self.y = y

        # 玩家属性
        self.max_hp = PLAYER_INITIAL_HP
        self.hp = self.max_hp
        self.attack = PLAYER_INITIAL_ATTACK
        self.defense = PLAYER_INITIAL_DEFENSE
        self.gold = PLAYER_INITIAL_GOLD

        # 背包系统
        self.inventory = {
            ITEM_KEY_YELLOW: 0,
            ITEM_KEY_BLUE: 0,
            ITEM_KEY_RED: 0,
            ITEM_POTION_RED: 0,
            ITEM_POTION_BLUE: 0,
            ITEM_SWORD: 0,
            ITEM_SHIELD: 0,
        }

        # 装备
        self.equipped_sword = 0
        self.equipped_shield = 0

        # 经验值和等级
        self.level = 1
        self.exp = 0
        self.exp_to_next_level = 100

    def move(self, dx, dy):
        """移动玩家"""
        self.x += dx
        self.y += dy

    def add_item(self, item_type, count=1):
        """添加道具到背包"""
        if item_type in self.inventory:
            self.inventory[item_type] += count

    def use_item(self, item_type, count=1):
        """使用道具"""
        if item_type in self.inventory and self.inventory[item_type] >= count:
            self.inventory[item_type] -= count

            # 处理道具效果
            if item_type == ITEM_POTION_RED:
                self.heal(50)
            elif item_type == ITEM_POTION_BLUE:
                self.restore_mp(30)  # 如果有魔法值系统
            elif item_type == ITEM_SWORD:
                self.equipped_sword += 1
                self.update_attack()
            elif item_type == ITEM_SHIELD:
                self.equipped_shield += 1
                self.update_defense()

            return True
        return False

    def has_item(self, item_type, count=1):
        """检查是否拥有指定数量的道具"""
        return item_type in self.inventory and self.inventory[item_type] >= count

    def heal(self, amount):
        """恢复生命值"""
        self.hp = min(self.max_hp, self.hp + amount)

    def take_damage(self, damage):
        """受到伤害"""
        actual_damage = max(1, damage - self.defense)
        self.hp = max(0, self.hp - actual_damage)
        return actual_damage

    def add_gold(self, amount):
        """增加金币"""
        self.gold += amount

    def spend_gold(self, amount):
        """花费金币"""
        if self.gold >= amount:
            self.gold -= amount
            return True
        return False

    def add_exp(self, amount):
        """增加经验值"""
        self.exp += amount
        while self.exp >= self.exp_to_next_level:
            self.level_up()

    def level_up(self):
        """升级"""
        self.exp -= self.exp_to_next_level
        self.level += 1

        # 提升属性
        self.max_hp += 20
        self.hp = self.max_hp  # 升级时恢复满血
        self.attack += 5
        self.defense += 2

        # 计算下一级所需经验
        self.exp_to_next_level = int(self.exp_to_next_level * 1.2)

        # 播放升级音效
        sound_manager.play_sound("level_up")

    def update_attack(self):
        """更新攻击力"""
        base_attack = PLAYER_INITIAL_ATTACK + (self.level - 1) * 5
        sword_bonus = self.equipped_sword * 10
        self.attack = base_attack + sword_bonus

    def update_defense(self):
        """更新防御力"""
        base_defense = PLAYER_INITIAL_DEFENSE + (self.level - 1) * 2
        shield_bonus = self.equipped_shield * 5
        self.defense = base_defense + shield_bonus

    def get_total_attack(self):
        """获取总攻击力"""
        return self.attack

    def get_total_defense(self):
        """获取总防御力"""
        return self.defense

    def is_alive(self):
        """检查是否存活"""
        return self.hp > 0

    def get_screen_pos(self, screen):
        """获取玩家在屏幕上的位置"""
        map_pixel_width = MAP_WIDTH * TILE_SIZE
        map_pixel_height = MAP_HEIGHT * TILE_SIZE
        offset_x = (screen.get_width() - map_pixel_width) // 2
        offset_y = (screen.get_height() - map_pixel_height) // 2

        screen_x = offset_x + self.x * TILE_SIZE
        screen_y = offset_y + self.y * TILE_SIZE

        return screen_x, screen_y

    def update(self):
        """更新玩家状态"""
        # 这里可以添加状态效果的更新逻辑
        pass

    def render(self, screen):
        """渲染玩家"""
        screen_x, screen_y = self.get_screen_pos(screen)

        # 绘制玩家
        player_rect = pygame.Rect(screen_x, screen_y, TILE_SIZE, TILE_SIZE)
        pygame.draw.rect(screen, BLUE, player_rect)
        pygame.draw.rect(screen, WHITE, player_rect, 2)

        # 绘制玩家符号
        font = pygame.font.Font(None, 24)
        text = font.render("P", True, WHITE)
        text_rect = text.get_rect(center=player_rect.center)
        screen.blit(text, text_rect)

    def get_status_info(self):
        """获取状态信息用于UI显示"""
        return {
            "level": self.level,
            "hp": self.hp,
            "max_hp": self.max_hp,
            "attack": self.attack,
            "defense": self.defense,
            "gold": self.gold,
            "exp": self.exp,
            "exp_to_next": self.exp_to_next_level,
            "inventory": self.inventory.copy(),
        }
