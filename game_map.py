import pygame
from constants import *


class GameMap:
    """游戏地图类"""

    def __init__(self):
        # 初始化地图数据 - 多层地图
        self.maps = {}
        self.init_maps()

    def init_maps(self):
        """初始化地图数据"""
        # 第1层地图
        floor1 = [
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 0, 1, 1, 5, 1, 1, 0, 3, 0, 1],
            [1, 0, 1, 4, 0, 0, 1, 0, 1, 0, 1],
            [1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1],
            [1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1],
            [1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1],
            [1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 0, 1, 1, 1, 6, 1, 1, 1, 8, 1],
            [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        ]

        # 第2层地图
        floor2 = [
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
            [1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 0, 1, 1, 1, 1, 1, 0, 3, 0, 1],
            [1, 0, 1, 0, 0, 0, 1, 0, 1, 3, 1],
            [1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1],
            [1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1],
            [1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1],
            [1, 3, 1, 0, 0, 0, 0, 0, 0, 4, 1],
            [1, 0, 1, 1, 1, 7, 1, 1, 1, 8, 1],
            [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        ]

        # 第3层地图
        floor3 = [
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
            [1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 0, 1, 1, 1, 1, 1, 0, 3, 0, 1],
            [1, 0, 1, 4, 0, 0, 1, 0, 1, 3, 1],
            [1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1],
            [1, 3, 0, 0, 1, 0, 1, 0, 0, 3, 1],
            [1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1],
            [1, 3, 1, 0, 0, 0, 0, 0, 0, 4, 1],
            [1, 0, 1, 1, 1, 10, 1, 1, 1, 0, 1],
            [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        ]

        self.maps[1] = floor1
        self.maps[2] = floor2
        self.maps[3] = floor3

        # 为其他楼层生成简单地图
        for floor in range(4, MAX_FLOORS + 1):
            self.maps[floor] = self.generate_simple_map(floor)

    def generate_simple_map(self, floor):
        """生成简单的地图布局"""
        map_data = []
        for y in range(MAP_HEIGHT):
            row = []
            for x in range(MAP_WIDTH):
                if x == 0 or x == MAP_WIDTH - 1 or y == 0 or y == MAP_HEIGHT - 1:
                    row.append(TILE_WALL)
                else:
                    row.append(TILE_EMPTY)
            map_data.append(row)

        # 添加一些随机元素
        import random

        # 添加楼梯
        if floor > 1:
            map_data[1][1] = TILE_STAIRS_DOWN
        if floor < MAX_FLOORS:
            map_data[MAP_HEIGHT - 2][MAP_WIDTH - 2] = TILE_STAIRS_UP

        # 添加一些怪物
        for _ in range(3 + floor // 2):
            x = random.randint(2, MAP_WIDTH - 3)
            y = random.randint(2, MAP_HEIGHT - 3)
            if map_data[y][x] == TILE_EMPTY:
                map_data[y][x] = TILE_MONSTER

        # 添加一些道具
        for _ in range(2):
            x = random.randint(2, MAP_WIDTH - 3)
            y = random.randint(2, MAP_HEIGHT - 3)
            if map_data[y][x] == TILE_EMPTY:
                map_data[y][x] = TILE_ITEM

        return map_data

    def get_tile(self, x, y, floor):
        if (
            floor in self.maps
            and 0 <= y < len(self.maps[floor])
            and 0 <= x < len(self.maps[floor][y])
        ):
            return self.maps[floor][y][x]
        return TILE_WALL

    def set_tile(self, x, y, floor, tile_type):
        if (
            floor in self.maps
            and 0 <= y < len(self.maps[floor])
            and 0 <= x < len(self.maps[floor][y])
        ):
            self.maps[floor][y][x] = tile_type

    def can_move_to(self, x, y, floor):
        tile_type = self.get_tile(x, y, floor)
        return tile_type not in [
            TILE_WALL,
            TILE_DOOR_YELLOW,
            TILE_DOOR_BLUE,
            TILE_DOOR_RED,
        ]

    def get_stairs_up_pos(self, floor):
        if floor in self.maps:
            for y in range(len(self.maps[floor])):
                for x in range(len(self.maps[floor][y])):
                    if self.maps[floor][y][x] == TILE_STAIRS_UP:
                        return x, y
        return 1, 1  # 默认位置

    def get_stairs_down_pos(self, floor):
        if floor in self.maps:
            for y in range(len(self.maps[floor])):
                for x in range(len(self.maps[floor][y])):
                    if self.maps[floor][y][x] == TILE_STAIRS_DOWN:
                        return x, y
        return 1, 1  # 默认位置

    def render(self, screen, floor):
        if floor not in self.maps:
            return

        map_data = self.maps[floor]

        # 计算地图在屏幕上的偏移量，使其居中
        map_pixel_width = MAP_WIDTH * TILE_SIZE
        map_pixel_height = MAP_HEIGHT * TILE_SIZE
        offset_x = (screen.get_width() - map_pixel_width) // 2
        offset_y = (screen.get_height() - map_pixel_height) // 2

        for y in range(len(map_data)):
            for x in range(len(map_data[y])):
                tile_type = map_data[y][x]
                color = TILE_COLORS.get(tile_type, BLACK)

                rect = pygame.Rect(
                    offset_x + x * TILE_SIZE,
                    offset_y + y * TILE_SIZE,
                    TILE_SIZE,
                    TILE_SIZE,
                )

                pygame.draw.rect(screen, color, rect)

                # 绘制边框
                if tile_type != TILE_EMPTY:
                    pygame.draw.rect(screen, WHITE, rect, 1)

                # 绘制特殊符号
                self.draw_tile_symbol(screen, tile_type, rect)

    def draw_tile_symbol(self, screen, tile_type, rect):
        """绘制地块符号"""
        font = pygame.font.Font(None, 20)
        symbol = ""

        if tile_type == TILE_STAIRS_UP:
            symbol = "↑"
        elif tile_type == TILE_STAIRS_DOWN:
            symbol = "↓"
        elif tile_type == TILE_MONSTER:
            symbol = "M"
        elif tile_type == TILE_ITEM:
            symbol = "I"
        elif tile_type == TILE_DOOR_YELLOW:
            symbol = "Y"
        elif tile_type == TILE_DOOR_BLUE:
            symbol = "B"
        elif tile_type == TILE_DOOR_RED:
            symbol = "R"
        elif tile_type == TILE_SHOP:
            symbol = "S"
        elif tile_type == TILE_NPC:
            symbol = "N"

        if symbol:
            text = font.render(symbol, True, BLACK)
            text_rect = text.get_rect(center=rect.center)
            screen.blit(text, text_rect)
